import { Calendar } from "lucide-react";
import Image from "next/image";

export default function AboutDryCreekVeterinaryHospital() {
  return (
    <div>
      <section id="about" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-10">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-2xl md:text-4xl font-bold text-gray-900">
                  About{" "}
                  <span className="text-blue-600 block">
                    Dry Creek Veterinary Hospital
                  </span>
                </h2>
                <button className="bg-blue-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Book Appointment
                </button>
              </div>
            </div>

            <div className="relative">
              <video
                autoPlay
                loop
                playsInline
                muted
                className="w-full h-80 object-cover rounded-tl-[20px] rounded-tr-[20px] rounded-br-[20px] rounded-bl-[100px]"
              >
                <source src="/About-Hospital-Video.mp4" type="video/mp4" />
              </video>
            </div>
          </div>
        </div>
      </section>
      <section
        id="team"
        className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100"
      >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-2xl md:text-4xl font-bold text-gray-900">
                  About Us{" "}
                </h2>
                <p className=" text-gray-600 leading-relaxed">
                  Dry Creek Veterinary Hospital was established in Galt,
                  California, in April of 1994. We promote friendly client
                  service and quality veterinary care in a clean, pleasant
                  environment.
                </p>
                <p className=" text-gray-600 leading-relaxed">
                  Our hospital is designed and equipped to provide the best
                  possible medical care for dogs and cats. It is our objective
                  to:
                </p>
                <ul className="space-y-3 text-gray-700 pl-5">
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <span>Provide preventive medical care</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <span>Provide immediate medical & surgical care</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <span>Treat your pet humanely and compassionately</span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="relative">
              <Image
                src="/images/view-cats-dogs-being-friends.webp"
                alt="Veterinary hospital interior"
                width={600}
                height={400}
                className="rounded-2xl shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
