'use client'

import Image from "next/image";
import React, { useState, useCallback } from 'react';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';

type ImageType = {
  id: number;
  src: string;
  alt: string;
  title: string;
};

const images: ImageType[] = [
  {
    id: 1,
    src: "../images/<PERSON>-<PERSON><PERSON>.webp",
    alt: 'Mountain landscape',
    title: 'Beautiful Mountains'
  },
  {
    id: 2,
    src: '../images/<PERSON>-<PERSON>.webp',
    alt: 'Forest path',
    title: 'Forest Trail'
  },
  {
    id: 3,
    src: '../images/Stacy-<PERSON>hoeun.webp',
    alt: 'Lake view',
    title: 'Serene Lake'
  },
  {
    id: 4,
    src: '../images/<PERSON>-<PERSON>.webp',
    alt: 'Ocean sunset',
    title: 'Ocean Sunset'
  },
  {
    id: 5,
    src: '../images/Stacy-Khoeun.webp',
    alt: 'Desert dunes',
    title: 'Desert Landscape'
  },
  {
    id: 6,
    src: '../images/Stacy-<PERSON>hoeun.webp',
    alt: 'Tropical beach',
    title: 'Tropical Paradise'
  },
  {
    id: 7,
    src: '../images/<PERSON>-<PERSON>.webp',
    alt: 'Snowy peaks',
    title: 'Snow Mountains'
  },
  {
    id: 8,
    src: '../images/Stacy-Khoeun.webp',
    alt: 'Valley view',
    title: 'Green Valley'
  }
];

export default function PhotoGallery() {

  const [selectedImage, setSelectedImage] = useState<ImageType | null>(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  const openModal = (image: ImageType, index: number) => {
    setSelectedImage(image);
    setCurrentIndex(index);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  const nextImage = useCallback(() => {
    const nextIndex = (currentIndex + 1) % images.length;
    setCurrentIndex(nextIndex);
    setSelectedImage(images[nextIndex]);
  }, [currentIndex]);

  const prevImage = useCallback(() => {
    const prevIndex = (currentIndex - 1 + images.length) % images.length;
    setCurrentIndex(prevIndex);
    setSelectedImage(images[prevIndex]);
  }, [currentIndex]);

  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    if (e.key === 'Escape') closeModal();
    if (e.key === 'ArrowRight') nextImage();
    if (e.key === 'ArrowLeft') prevImage();
  }, [nextImage, prevImage]);

  React.useEffect(() => {
    if (selectedImage) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
      return () => {
        document.removeEventListener('keydown', handleKeyDown);
        document.body.style.overflow = 'unset';
      };
    }
  }, [selectedImage, handleKeyDown]);

  return (
    <div>
      <section id="about" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-10">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-2xl md:text-4xl font-bold text-gray-900">
                  Photo <span className="text-blue-600">Gallery</span>
                </h2>
                <p>
                  If you would like to submit a photo, please click the button
                  below.
                </p>
              </div>
            </div>

            <div className="relative">
              <Image
                src="/images/photo-gallery.webp"
                alt="Veterinary hospital interior"
                width={600}
                height={400}
                className="rounded-2xl shadow-xl rounded-tl-[20px] rounded-tr-[20px] rounded-br-[20px] rounded-bl-[100px]"
              />
            </div>
          </div>
        </div>
      </section>

      <section id="team" className="py-20 bg-blue-600">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-1 gap-12 items-center text-center">
            <div className="space-y-6">
              <h2 className="text-2xl md:text-4xl font-bold text-white">
                We Love Our Patients
              </h2>
              <p className="text-white max-w-[45rem] m-auto">
                At Dry Creek Veterinary Hospital, our patients mean the world to
                us. They make what we do every day worth it. Check out some of
                the adorable faces of our wonderful patients. If you would like
                to submit a photo to be featured on our page, please fill out
                the form below.
              </p>
            </div>
          </div>
        </div>
      </section>

      <section id="services" className="py-20 bg-white">
        <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
              {/* Header */}
              <div className="max-w-7xl mx-auto mb-12">
                <h1 className="text-4xl font-bold text-center text-gray-900 mb-4">
                  Photo Gallery
                </h1>
                <p className="text-center text-gray-600 max-w-2xl mx-auto">
                  Click on any image to view it in full size. Use arrow keys or navigation buttons to browse through the gallery.
                </p>
              </div>
        
              {/* Gallery Grid */}
              <div className="max-w-7xl mx-auto">
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                  {images.map((image, index) => (
                    <div
                      key={image.id}
                      className="group cursor-pointer overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2"
                      onClick={() => openModal(image, index)}
                    >
                      <div className="aspect-w-4 aspect-h-3 relative">
                        <Image
                          src={image.src}
                          alt={image.alt}
                          className="w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity duration-300 flex items-center justify-center">
                          <div className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-center">
                            <h3 className="text-lg font-semibold mb-2">{image.title}</h3>
                            <p className="text-sm">Click to view</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
        
              {/* Modal */}
              {selectedImage && (
                <div 
                  className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-90"
                  onClick={closeModal}
                >
                  {/* Close button */}
                  <button
                    onClick={closeModal}
                    className="absolute top-4 right-4 z-60 p-2 text-white hover:text-gray-300 transition-colors duration-200"
                    aria-label="Close modal"
                  >
                    <X size={32} />
                  </button>
        
                  {/* Navigation buttons */}
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      prevImage();
                    }}
                    className="absolute left-4 top-1/2 -translate-y-1/2 z-60 p-2 text-white hover:text-gray-300 transition-colors duration-200"
                    aria-label="Previous image"
                  >
                    <ChevronLeft size={40} />
                  </button>
        
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      nextImage();
                    }}
                    className="absolute right-4 top-1/2 -translate-y-1/2 z-60 p-2 text-white hover:text-gray-300 transition-colors duration-200"
                    aria-label="Next image"
                  >
                    <ChevronRight size={40} />
                  </button>
        
                  {/* Image container */}
                  <div 
                    className="max-w-4xl max-h-full flex flex-col items-center"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Image
                      src={selectedImage.src}
                      alt={selectedImage.alt}
                      className="max-w-full max-h-[80vh] object-contain rounded-lg shadow-2xl"
                    />
                    <div className="mt-4 text-center">
                      <h2 className="text-2xl font-bold text-white mb-2">{selectedImage.title}</h2>
                      <p className="text-gray-300">
                        {currentIndex + 1} of {images.length}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
      </section>
    </div>
  );
}
