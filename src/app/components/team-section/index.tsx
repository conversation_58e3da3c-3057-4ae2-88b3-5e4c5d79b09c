import { ChevronRight } from "lucide-react";
import Image from "next/image";

const TeamSection = () => {
  return (
    <section
      id="team"
      className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-6">
            <h2 className="text-2xl md:text-4xl font-bold text-gray-900">
              Your Trusted Veterinarian & Staff
            </h2>
            <p className=" text-gray-600 leading-relaxed">
              Meet the dedicated and compassionate veterinarian at Dry Creek
              Veterinary Hospital in Galt, CA. Our skilled veterinarian and
              staff are committed to providing exceptional care for your pets.
              We take pride in ensuring your pets receive the best possible
              treatment, with a focus on their health and well-being.
            </p>
            <button className="bg-blue-600 text-white px-8 py-3 rounded-full font-semibold hover:bg-blue-700 transition-colors flex items-center">
              Meet Our Team
              <ChevronRight className="w-5 h-5 ml-2" />
            </button>
          </div>

          <div className="relative">
            <Image
              src="/images/A-collage-displaying-two-pictures-of-a-dog-and-a-cat.webp"
              alt="Veterinary team"
              width={600}
              height={400}
              className="rounded-2xl shadow-xl"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default TeamSection;
