"use client";
import {
  aboutDropDownMenuItem,
  resoucesDropDownMenuItem,
} from "@/app/constant/constant";
import { ChevronDown, ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useRef, useState } from "react";
import veternaryLogo from "../../../../public/logos/Dry-creek-veterinary-hospital-logo.webp";

export default function Header() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [activeMenu, setActiveMenu] = useState<string | null>(null);
  const [isServiceMenuOpen, setIsServiceMenuOpen] = useState(false);
  const [isAboutMenuOpen, setIsAboutMenuOpen] = useState(false);

  const router = useRouter();
  const pathName = usePathname();

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleMenuClick = (menu: string) => {
    setActiveMenu(activeMenu === menu ? null : menu);
  };

  const isActive = (path: string) => pathName === path;

  return (
    <header className="bg-white fixed z-50 w-full">
      {/* Mobile Header */}
      <div className="md:hidden w-full mb-4 pt-4">
        <div className="container  mx-auto flex items-center justify-between px-4">
          <div>
            <Image
              src={veternaryLogo}
              alt="Veterinary Medical Center Logo"
              loading="eager"
              className="cursor-pointer w-20 p-1 rounded-md"
              onClick={() => router?.push("/")}
            />
          </div>
          <button
            onClick={toggleMobileMenu}
            className="flex items-center px-4 py-2"
            aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
          >
            {isMobileMenuOpen ? (
              <svg
                className="w-8 h-8"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            ) : (
              <svg
                className="w-8 h-8"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16m-7 6h7"
                />
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* Desktop Navigation */}
      <div className="w-full">
        <div className="container mx-auto flex justify-between items-center">
          <nav className="hidden md:flex h-20 w-full items-center justify-between bg-white p-4">
            <div className="pl-3">
              <Image
                src={veternaryLogo}
                alt="Veterinary Medical Center Logo"
                loading="eager"
                width={800}
                height={762}
                className="h-20 w-auto bg-white p-1 rounded"
                onClick={() => router?.push("/")}
              />
            </div>

            <div className="flex items-center space-x-8">
              <Link href="/" className="text-black text-[18px]">
                Home
              </Link>

              {/* About Dropdown */}
              <div className="relative" ref={dropdownRef}>
                <div className="dropdown-service inline-block relative">
                  <Link href="/pages/about-dry-creek-veterinary-hospital">
                    <button
                      onMouseEnter={() => setIsAboutMenuOpen(true)}
                      onMouseLeave={() => setIsAboutMenuOpen(false)}
                      onClick={() => router?.push("/")}
                      className="rounded inline-flex items-center"
                    >
                      <span className="mr-1 text-[18px]">About Us</span>
                      <ChevronDown className="h-4 w-4 transition-transform" />
                    </button>
                  </Link>
                  <ul
                    className={`dropdown-menu-service absolute w-56 mt-[2px] bg-white shadow-lg rounded-md z-50 ${
                      isAboutMenuOpen ? "block" : "hidden"
                    }`}
                    onMouseEnter={() => setIsAboutMenuOpen(true)}
                    onMouseLeave={() => setIsAboutMenuOpen(false)}
                  >
                    {aboutDropDownMenuItem?.map((item, index) => (
                      <li key={index}>
                        <Link
                          href={item?.path || "#"}
                          className={`block px-4 py-2 hover:text-blue-600  ${
                            isActive(item.path) ? "" : ""
                          }`}
                        >
                          {item.label}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <Link href="/" className="text-[18px]">
                Services
              </Link>

              {/* Resources Dropdown */}
              <div className="relative" ref={dropdownRef}>
                <div className="dropdown-service inline-block relative">
                  <button
                    onMouseEnter={() => setIsServiceMenuOpen(true)}
                    onMouseLeave={() => setIsServiceMenuOpen(false)}
                    onClick={() => router?.push("/")}
                    className="rounded inline-flex items-center"
                  >
                    <span className="mr-1 text-[18px]">Resources</span>
                    <ChevronDown className="h-4 w-4 transition-transform" />
                  </button>
                  <ul
                    className={`dropdown-menu-service absolute w-56 mt-[2px] bg-white shadow-lg rounded-md z-50 ${
                      isServiceMenuOpen ? "block" : "hidden"
                    }`}
                    onMouseEnter={() => setIsServiceMenuOpen(true)}
                    onMouseLeave={() => setIsServiceMenuOpen(false)}
                  >
                    {resoucesDropDownMenuItem?.map((item, index) => (
                      <li key={index}>
                        <Link
                          href={item?.path || "#"}
                          className={`block px-4 py-2 hover:text-blue-600 ${
                            isActive(item.path) ? "" : ""
                          }`}
                        >
                          {item.label}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <Link href="/" className="text-[18px]">
                Online Pharmacy
              </Link>
              <Link href="/" className="text-[18px]">
                Contact
              </Link>
              <Link
                href="/"
                className="px-4 py-2 font-semibold rounded-lg bg-blue-600 !text-white"
              >
                APPOINTMENT
              </Link>
            </div>
          </nav>
        </div>
      </div>

      {/* Mobile Phone Info */}
      {/* <div className="flex md:hidden">
        <div className="text-sm tracking-wide flex items-center pb-2 px-4">
          <p className="flex items-center font-semibold">
            <Phone size={16} className="mr-1" />
            <a href="tel:5305580541" className="hover:text-sky-300">(*************</a>
          </p>
        </div>
      </div> */}

      {/* Mobile Dropdown Menu */}
      <div
        className={`md:hidden ${
          isMobileMenuOpen ? "max-h-[calc(100vh-90px)]" : "max-h-0"
        } overflow-y-auto transition-all duration-300 ease-in-out bg-blue-600`}
      >
        <nav className="flex flex-col space-y-2 pt-4 px-4 pb-6">
          <Link
            href="/"
            className={` mb-2 py-2 px-2 text-white rounded-sm ${
              isActive("/") ? "!text-yellow-300" : ""
            }`}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Home
          </Link>

          {/* About Mobile */}
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => {
                router?.push("/");
                setIsMobileMenuOpen(false);
              }}
              className={` py-2 flex text-white items-center w-full px-2 ${
                isActive("/aboutus/") || pathName?.includes("/aboutus/")
                  ? "text-blue-600"
                  : ""
              }`}
            >
              About Us
              {activeMenu === "aboutus" ? (
                <ChevronDown
                  className="ml-2"
                  size={16}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleMenuClick("aboutus");
                  }}
                />
              ) : (
                <ChevronRight
                  className="ml-2"
                  size={16}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleMenuClick("aboutus");
                  }}
                />
              )}
            </button>
            {activeMenu === "aboutus" && (
              <ul className="flex flex-col space-y-3 py-2  text-sm">
                {aboutDropDownMenuItem?.map((item, index) => (
                  <li key={index} className="flex">
                    <Link
                      href={item?.path || "#"}
                      className={`flex-1 py-2 px-4 text-white rounded-sm ${
                        isActive(`${item?.path}/`) ? "text-blue-600" : ""
                      }`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {item.label}
                    </Link>
                  </li>
                ))}
              </ul>
            )}
          </div>

          <Link
            href="/"
            className={`mb-2 py-2 px-2 text-white rounded-sm ${
              isActive("/contact/") ? "text-blue-600" : ""
            }`}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Services
          </Link>

          {/* Resources Mobile */}
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => {
                router?.push("/");
                setIsMobileMenuOpen(false);
              }}
              className={`py-2 flex items-center w-full text-white px-2 ${
                [
                  "/resources/",
                  "/veterinary-lab-sacramento/",
                  "/veterinary-hospital-gallery/",
                  "/faqs/",
                ].includes(pathName)
                  ? " text-blue-600"
                  : ""
              }`}
            >
              Resources
              {activeMenu === "resources" ? (
                <ChevronDown
                  className="ml-2"
                  size={16}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleMenuClick("resources");
                  }}
                />
              ) : (
                <ChevronRight
                  className="ml-2"
                  size={16}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleMenuClick("resources");
                  }}
                />
              )}
            </button>
            {activeMenu === "resources" && (
              <ul className="flex flex-col space-y-3 py-2  text-sm">
                {resoucesDropDownMenuItem?.map((item, index) => (
                  <li key={index} className="flex">
                    <Link
                      href={item?.path || "#"}
                      className={`flex-1 py-2 px-4 text-white rounded-sm ${
                        isActive(`${item?.path}/`) ? "text-blue-600" : ""
                      }`}
                      onClick={() => setIsMobileMenuOpen(false)}
                    >
                      {item?.label}
                    </Link>
                  </li>
                ))}
              </ul>
            )}
          </div>

          <Link
            href="/"
            className={`mb-2 py-2 px-2 text-white rounded-sm ${
              isActive("/contact/") ? "text-blue-600" : ""
            }`}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Online Pharmacy
          </Link>

          <Link
            href="/"
            className={`mb-2 py-2 px-2 text-white rounded-sm ${
              isActive("/contact/") ? "text-blue-600" : ""
            }`}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Contact Us
          </Link>

          <Link
            href="/"
            className={`mb-2 py-2 px-2 rounded-lg bg-white`}
            onClick={() => setIsMobileMenuOpen(false)}
          >
            APPOINTMENT
          </Link>
        </nav>
      </div>
    </header>
  );
}
