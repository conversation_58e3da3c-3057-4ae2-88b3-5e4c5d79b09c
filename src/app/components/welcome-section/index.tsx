'use client'
import Image from "next/image";
import { Calendar, Clock, MapPin, Phone } from "lucide-react";

const WelcomeSection = () => {
    return (
        <section
        id="home"
        className="pt-16 bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen flex items-center"
      >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-20">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h1 className=" text-2xl lg:text-4xl font-bold text-gray-900 leading-tight">
                  Welcome to
                  <span className="text-blue-600 block">
                    Dry Creek Veterinary
                  </span>
                  <span className="text-2xl lg:text-3xl font-medium text-gray-600 block mt-2">
                    Hospital
                  </span>
                </h1>
                <p className=" text-gray-600 max-w-lg">
                  Dry Creek Veterinary Hospital has been serving the Galt,
                  California community since 1995, dedicated to providing
                  exceptional client service and high-quality veterinary care in
                  a welcoming environment. Our facility is thoughtfully designed
                  and fully equipped to ensure the best possible medical
                  treatment for your beloved dogs and cats.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <button className="bg-blue-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-blue-700 transition-colors flex items-center justify-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Book Appointment
                </button>
                <a
                  href="tel:**********"
                  className="border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-full text-lg font-semibold hover:bg-blue-600 hover:text-white transition-colors flex items-center justify-center"
                >
                  <Phone className="w-5 h-5 mr-2" />
                  Emergency Call
                </a>
              </div>

              <div className="flex items-center space-x-6 pt-4">
                <div className="flex items-center">
                  <MapPin className="w-5 h-5 text-gray-500 mr-2" />
                  <span className="text-gray-600">Galt, California</span>
                </div>
                <div className="flex items-center">
                  <Clock className="w-5 h-5 text-gray-500 mr-2" />
                  <span className="text-gray-600">Open 24/7</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="relative z-10">
                <Image
                  src="/images/friendly-veterinarian-with-pet-animal-vector.webp"
                  alt="Veterinarian with pets"
                  width={600}
                  height={400}
                  className="rounded-3xl shadow-2xl"
                />
              </div>
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-blue-200 rounded-full opacity-50"></div>
              <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-green-200 rounded-full opacity-30"></div>
            </div>
          </div>
        </div>
      </section>
    );
};

export default WelcomeSection;
