import Link from "next/link";
import React from "react";

const BookAppointmentSection = () => {
  return (
    <div className="flex h-[350px]">
      {/* Left side - 30% */}
      <div className="basis-[30%] relative">
        <img
          src="/images/Dry-creek-veterinary-hospital.jpg"
          alt="Dry Creek Veterinary Hospital"
          className="w-full h-full object-cover"
        />
      </div>

      {/* Right side - 70% with curved effect */}
      <div className="basis-[70%] flex items-center justify-start bg-[#0a4a8a] relative overflow-hidden">
        {/* Curved overlay effect */}
        <div className="absolute left-0 top-0 h-full w-[200px] bg-[#0a4a8a] rounded-l-[200px]" />

        {/* Content */}
        <div className="relative w-full text-white px-12 text-left mb-14">
          <h1 className="text-2xl font-bold mb-3 md:text-3xl">
            Book an Appointment
          </h1>

          <p className="text-xs leading-relaxed mb-2">
            We proudly serve the pets of Galt, Lodi, Elk Grove, Acampo, Woodbridge,
            Stockton, and the surrounding areas. We can’t wait to see you! You can
            use the button below to make an appointment request.
          </p>

          <div className="pt-4">
            <Link
                href="/"
                className="inline-block bg-[#0fab89] text-sm hover:bg-[#0fab89] text-white py-3 px-8 rounded-lg"
            >
                BOOK APPOINTMENT
            </Link>
        </div>
        </div>
      </div>
    </div>
  );
};

export default BookAppointmentSection;
