image: node:18

stages:
  - build
  - deploy

before_script:
  - npm install

build_project:
  stage: build
  script:
    - npm run build
  artifacts:
    paths:
      - out/
  except:
    changes:
      - ".gitlab-ci.yml"
      - "README.md"
  only:
    - demo1
    - demo2
    - demo3
  tags:
    - git-cloud

# Deploy for develop branch (demo1.myvethub.com)
deploy_develop_demo1:
  stage: deploy
  script:
    - npx wrangler pages deploy ./out --project-name="$DEMO1_CLOUDFLARE_PROJECT_NAME" --branch="$CI_COMMIT_BRANCH"
  only:
    - demo1
  environment:
    name: production-demo1
    url: https://demo1.myvethub.com
  variables:
    CLOUDFLARE_API_TOKEN: $VMC_CLOUDFLARE_API_TOKEN
  tags:
    - git-cloud

# Deploy for develop branch (demo2.myvethub.com)
deploy_develop_demo2:
  stage: deploy
  script:
    - npx wrangler pages deploy ./out --project-name="$DEMO2_CLOUDFLARE_PROJECT_NAME" --branch="$CI_COMMIT_BRANCH"
  only:
    - demo2
  environment:
    name: production-demo2
    url: https://demo2.myvethub.com
  variables:
    CLOUDFLARE_API_TOKEN: $VMC_CLOUDFLARE_API_TOKEN
  tags:
    - git-cloud

# Deploy for myvethub_prod branch (demo3.myvethub.com)
deploy_sacvet_prod:
  stage: deploy
  script:
    - npx wrangler pages deploy ./out --project-name="$DEMO3_CLOUDFLARE_PROJECT_NAME" --branch="$CI_COMMIT_BRANCH"
  only:
    - demo3
  environment:
    name: production-demo3
    url: https://demo3.myvethub.com
  variables:
    CLOUDFLARE_API_TOKEN: $VMC_CLOUDFLARE_API_TOKEN
  tags:
    - git-cloud